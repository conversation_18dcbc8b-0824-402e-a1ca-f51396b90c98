import 'package:bloomg_flutter/auth/cubit/signup_cubit.dart';
import 'package:bloomg_flutter/auth/models/confirmed_password.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/name.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository_impl.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/navigation/auth_navigation.dart';
import 'package:bloomg_flutter/shared/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CreateAccountPage extends StatelessWidget {
  const CreateAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Bloc<PERSON>rovider(
        create: (_) => SignupCubit(const AuthRepositoryImpl()),
        child: const CreateAccountForm(),
      ),
    );
  }
}

class CreateAccountForm extends StatefulWidget {
  const CreateAccountForm({super.key});

  @override
  State<CreateAccountForm> createState() => _CreateAccountFormState();
}

class _CreateAccountFormState extends State<CreateAccountForm> {
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _fullNameController.addListener(() {
      context.read<SignupCubit>().nameChanged(_fullNameController.text);
    });
    _emailController.addListener(() {
      context.read<SignupCubit>().emailChanged(_emailController.text);
    });
    _passwordController.addListener(() {
      context.read<SignupCubit>().passwordChanged(_passwordController.text);
    });
    _confirmPasswordController.addListener(() {
      context
          .read<SignupCubit>()
          .confirmedPasswordChanged(_confirmPasswordController.text);
    });
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SignupCubit, SignupState>(
      listener: (context, state) {
        if (state.status.isSubmissionFailure) {
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Sign Up Failure'),
                backgroundColor: AppColors.error,
              ),
            );
        }
        if (state.status.isSubmissionSuccess) {
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(
              const SnackBar(
                content: Text('Account created successfully!'),
                backgroundColor: AppColors.success,
              ),
            );
        }
      },
      child: SafeArea(
        child: Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
          child: Column(
            children: [
              const SizedBox(height: AppDimensions.spacingHuge),
              const BloomgLogo(),
              const SizedBox(height: AppDimensions.spacingMassive),
              // Create Account Form
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Create Account',
                        style: AppTextStyles.heading1,
                      ),
                      const SizedBox(height: AppDimensions.spacingXXXL),
                      // Create Account Form Container
                      AuthFormContainer(
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Full Name Field
                              BlocBuilder<SignupCubit, SignupState>(
                                buildWhen: (previous, current) =>
                                    previous.name != current.name ||
                                    previous.nameTouched != current.nameTouched,
                                builder: (context, state) {
                                  return ValidatedAuthFormField(
                                    label: 'Full Name',
                                    controller: _fullNameController,
                                    keyboardType: TextInputType.name,
                                    onFocusChange: (hasFocus) {
                                      if (hasFocus) {
                                        context
                                            .read<SignupCubit>()
                                            .nameTouched();
                                      }
                                    },
                                    errorText: state.name.error?.message,
                                    showError: state.nameTouched &&
                                        state.name.isNotValid,
                                  );
                                },
                              ),
                              const SizedBox(height: AppDimensions.spacingXL),
                              // Email Field
                              BlocBuilder<SignupCubit, SignupState>(
                                buildWhen: (previous, current) =>
                                    previous.email != current.email ||
                                    previous.emailTouched !=
                                        current.emailTouched,
                                builder: (context, state) {
                                  return ValidatedAuthFormField(
                                    label: 'Your Email Address',
                                    controller: _emailController,
                                    keyboardType: TextInputType.emailAddress,
                                    onFocusChange: (hasFocus) {
                                      if (hasFocus) {
                                        context
                                            .read<SignupCubit>()
                                            .emailTouched();
                                      }
                                    },
                                    errorText: state.email.error?.message,
                                    showError: state.emailTouched &&
                                        state.email.isNotValid,
                                  );
                                },
                              ),
                              const SizedBox(height: AppDimensions.spacingXL),
                              // Password Field
                              BlocBuilder<SignupCubit, SignupState>(
                                buildWhen: (previous, current) =>
                                    previous.password != current.password ||
                                    previous.passwordTouched !=
                                        current.passwordTouched,
                                builder: (context, state) {
                                  return ValidatedPasswordField(
                                    label: 'Your Password',
                                    controller: _passwordController,
                                    onFocusChange: (hasFocus) {
                                      if (hasFocus) {
                                        context
                                            .read<SignupCubit>()
                                            .passwordTouched();
                                      }
                                    },
                                    errorText: state.password.error?.message,
                                    showError: state.passwordTouched &&
                                        state.password.isNotValid,
                                  );
                                },
                              ),
                              const SizedBox(height: AppDimensions.spacingXL),
                              // Confirm Password Field
                              BlocBuilder<SignupCubit, SignupState>(
                                buildWhen: (previous, current) =>
                                    previous.confirmedPassword !=
                                        current.confirmedPassword ||
                                    previous.confirmedPasswordTouched !=
                                        current.confirmedPasswordTouched,
                                builder: (context, state) {
                                  return ValidatedPasswordField(
                                    label: 'Confirm Password',
                                    controller: _confirmPasswordController,
                                    onFocusChange: (hasFocus) {
                                      if (hasFocus) {
                                        context
                                            .read<SignupCubit>()
                                            .confirmedPasswordTouched();
                                      }
                                    },
                                    errorText:
                                        state.confirmedPassword.error?.message,
                                    showError: state.confirmedPasswordTouched &&
                                        state.confirmedPassword.isNotValid,
                                  );
                                },
                              ),
                              const SizedBox(height: AppDimensions.spacingXXL),
                              // Create Account Button
                              BlocBuilder<SignupCubit, SignupState>(
                                buildWhen: (previous, current) =>
                                    previous.status != current.status,
                                builder: (context, state) {
                                  return AuthButton(
                                    text: 'Create Account',
                                    isLoading:
                                        state.status.isSubmissionInProgress,
                                    onPressed: state.status.isValidated
                                        ? () => context
                                            .read<SignupCubit>()
                                            .signUpFormSubmitted()
                                        : null,
                                  );
                                },
                              ),
                              const SizedBox(height: AppDimensions.spacingXL),
                              // Already have account Link
                              Center(
                                child: TextButton(
                                  onPressed: () => AuthNavigation.back(context),
                                  child: const Text(
                                    'Already have an account? Log in',
                                    style: AppTextStyles.linkPlain,
                                  ),
                                ),
                              ),
                              const SizedBox(height: AppDimensions.spacingS),
                              // Forgot Password Link
                              Center(
                                child: TextButton(
                                  onPressed: () =>
                                      AuthNavigation.toForgotPassword(context),
                                  child: const Text(
                                    'Forgot password?',
                                    style: AppTextStyles.linkPlain,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: AppDimensions.spacingXXXL),
                      // Support Information
                      const SupportFooter(),
                      const SizedBox(height: AppDimensions.spacingXXXL),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
