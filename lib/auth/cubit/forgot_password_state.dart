part of 'forgot_password_cubit.dart';

/// {@template forgot_password_state}
/// The state of the forgot password form.
/// {@endtemplate}
class ForgotPasswordState extends Equatable {
  /// {@macro forgot_password_state}
  const ForgotPasswordState({
    this.email = const Email.pure(),
    this.status = FormStatus.pure,
    this.errorMessage,
  });

  /// The email input.
  final Email email;

  /// The status of the form.
  final FormStatus status;

  /// The error message.
  final String? errorMessage;

  /// Creates a copy of this state with the given fields replaced.
  ForgotPasswordState copyWith({
    Email? email,
    FormStatus? status,
    String? errorMessage,
  }) {
    return ForgotPasswordState(
      email: email ?? this.email,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [email, status, errorMessage];
}
