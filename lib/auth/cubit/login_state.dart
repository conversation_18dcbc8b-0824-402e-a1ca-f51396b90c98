part of 'login_cubit.dart';

/// {@template login_state}
/// The state of the login form.
/// {@endtemplate}
class LoginState extends Equatable {
  /// {@macro login_state}
  const LoginState({
    this.email = const Email.pure(),
    this.password = const Password.pure(),
    this.status = FormStatus.pure,
    this.errorMessage,
  });

  /// The email input.
  final Email email;

  /// The password input.
  final Password password;

  /// The status of the form.
  final FormStatus status;

  /// The error message.
  final String? errorMessage;

  /// Creates a copy of this state with the given fields replaced.
  LoginState copyWith({
    Email? email,
    Password? password,
    FormStatus? status,
    String? errorMessage,
  }) {
    return LoginState(
      email: email ?? this.email,
      password: password ?? this.password,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [email, password, status, errorMessage];
}
