import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:equatable/equatable.dart';

part 'forgot_password_state.dart';

/// {@template forgot_password_cubit}
/// A [Cubit] which manages the forgot password form state.
/// {@endtemplate}
class ForgotPasswordCubit extends Cubit<ForgotPasswordState> {
  /// {@macro forgot_password_cubit}
  ForgotPasswordCubit(this._authRepository)
      : super(const ForgotPasswordState());

  final AuthRepository _authRepository;

  /// Updates the email input.
  void emailChanged(String value) {
    final email = Email.dirty(value);
    emit(
      state.copyWith(
        email: email,
        status: email.isValid ? FormStatus.valid : FormStatus.invalid,
      ),
    );
  }

  /// Submits the forgot password form.
  Future<void> sendPasswordResetEmail() async {
    if (!state.status.isValidated) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));
    try {
      await _authRepository.sendPasswordResetEmail(
        email: state.email.value,
      );
      emit(state.copyWith(status: FormStatus.submissionSuccess));
    } on SendPasswordResetEmailFailure catch (e) {
      emit(
        state.copyWith(
          errorMessage: e.message,
          status: FormStatus.submissionFailure,
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: FormStatus.submissionFailure));
    }
  }
}
