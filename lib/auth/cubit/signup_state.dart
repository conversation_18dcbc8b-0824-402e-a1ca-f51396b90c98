part of 'signup_cubit.dart';

/// {@template signup_state}
/// The state of the signup form.
/// {@endtemplate}
class SignupState extends Equatable {
  /// {@macro signup_state}
  const SignupState({
    this.name = const Name.pure(),
    this.email = const Email.pure(),
    this.password = const Password.pure(),
    this.confirmedPassword = const ConfirmedPassword.pure(),
    this.status = FormStatus.pure,
    this.errorMessage,
  });

  /// The name input.
  final Name name;

  /// The email input.
  final Email email;

  /// The password input.
  final Password password;

  /// The confirmed password input.
  final ConfirmedPassword confirmedPassword;

  /// The status of the form.
  final FormStatus status;

  /// The error message.
  final String? errorMessage;

  /// Creates a copy of this state with the given fields replaced.
  SignupState copyWith({
    Name? name,
    Email? email,
    Password? password,
    ConfirmedPassword? confirmedPassword,
    FormStatus? status,
    String? errorMessage,
  }) {
    return SignupState(
      name: name ?? this.name,
      email: email ?? this.email,
      password: password ?? this.password,
      confirmedPassword: confirmedPassword ?? this.confirmedPassword,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        name,
        email,
        password,
        confirmedPassword,
        status,
        errorMessage,
      ];
}
