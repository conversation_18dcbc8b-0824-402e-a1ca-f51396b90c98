import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:flutter/material.dart';

/// Enhanced password field widget with validation error display and visibility
/// toggle
class ValidatedPasswordField extends StatefulWidget {
  const ValidatedPasswordField({
    required this.label,
    required this.controller,
    required this.onFocusChange,
    this.errorText,
    this.showError = false,
    super.key,
  });

  final String label;
  final TextEditingController controller;
  final void Function(bool hasFocus) onFocusChange;
  final String? errorText;
  final bool showError;

  @override
  State<ValidatedPasswordField> createState() => _ValidatedPasswordFieldState();
}

class _ValidatedPasswordFieldState extends State<ValidatedPasswordField> {
  bool _obscureText = true;

  void _togglePasswordVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    final hasError = widget.showError && widget.errorText != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: AppTextStyles.bodyLarge,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Focus(
          onFocusChange: widget.onFocusChange,
          child: TextFormField(
            controller: widget.controller,
            style: AppTextStyles.input,
            obscureText: _obscureText,
            decoration: InputDecoration(
              filled: true,
              fillColor: AppColors.inputBackground,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                borderSide: hasError
                    ? const BorderSide(color: AppColors.error)
                    : BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                borderSide: hasError
                    ? const BorderSide(color: AppColors.error)
                    : BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                borderSide: hasError
                    ? const BorderSide(color: AppColors.error, width: 2)
                    : const BorderSide(color: AppColors.primary, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingM,
                vertical: AppDimensions.paddingM,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureText ? Icons.visibility : Icons.visibility_off,
                  color: AppColors.textSecondary,
                ),
                onPressed: _togglePasswordVisibility,
              ),
            ),
          ),
        ),
        if (hasError) ...[
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            widget.errorText!,
            style: const TextStyle(
              color: AppColors.error,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }
}
